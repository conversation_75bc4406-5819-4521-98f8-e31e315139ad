import { Request, tryAbsoluteURL } from "crawlee";
import { getOrInitQueue } from "../shared.js";

/**
 * Extract URLs from elements and create Request objects
 * @param {CheerioAPI} $ - Cheerio instance
 * @param {string} selector - CSS selector for links
 * @param {string} baseUrl - Base URL for resolving relative URLs
 * @param {string} label - Request label
 * @param {object} userData - User data to attach to requests
 * @param {function} log - Logger function
 * @returns {Request[]} Array of Request objects
 */
export function extractRequestsFromLinks(
  $,
  selector,
  baseUrl,
  label,
  userData,
  log
) {
  const requests = [];

  $(selector).each((_, element) => {
    const href = $(element).attr("href");
    if (href) {
      const fullUrl = tryAbsoluteURL(href, baseUrl);
      if (fullUrl) {
        requests.push(
          new Request({
            url: fullUrl,
            label,
            userData,
          })
        );

        if (log) {
          log.debug(`🔗 Preparing ${label}: ${fullUrl}`);
        }
      }
    }
  });

  return requests;
}

/**
 * Extract and enqueue requests using shared queue
 * @param {object} params - Parameters object
 * @param {CheerioAPI} params.$ - Cheerio instance
 * @param {string} params.selector - CSS selector for links
 * @param {string} params.baseUrl - Base URL for resolving relative URLs
 * @param {string} params.label - Request label
 * @param {object} params.userData - User data to attach to requests
 * @param {function} params.addRequests - addRequests function from context (fallback)
 * @param {object} params.sharedQueue - Shared queue instance (optional)
 * @param {function} params.log - Logger function
 * @returns {Promise<number>} Number of requests added
 */
export async function extractAndEnqueueRequests({
  $,
  selector,
  baseUrl,
  label,
  userData,
  addRequests,
  sharedQueue,
  log,
}) {
  const requests = extractRequestsFromLinks(
    $,
    selector,
    baseUrl,
    label,
    userData,
    log
  );

  if (requests.length > 0) {
    // Always use addRequests from context instead of shared queue
    await addRequests(requests);
    if (log) {
      log.info(
        `✅ Added ${requests.length} ${label} requests using addRequests`
      );
    }
  }

  return requests.length;
}
