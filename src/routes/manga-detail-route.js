import * as extractors from "../extractors/index.js";
import { extractAndEnqueueRequests } from "../utils/url-helpers.js";
import { getOrInitQueue } from "../shared.js";

export async function handleMangaDetail({
  parseWithCheerio,
  log,
  request,
  addRequests,
}) {
  const sharedQueue = await getOrInitQueue();
  const { site } = request.userData;
  log.info(`👉 Scraping detail page ${request.url}`);

  const $ = await parseWithCheerio();

  const mangaDetail = extractors.mangaDetail($, site?.selectors?.detailManga);
  log.info(`✅ Scraped ${mangaDetail?.title}`);

  // Enqueue chapter links
  const chapterLinksCount = $(site.selectors.detailManga.chapterLink).length;
  log.info(`📖 Found ${chapterLinksCount} chapter links for manga: ${mangaDetail?.title}`);

  if (chapterLinksCount > 0) {
    const addedCount = await extractAndEnqueueRequests({
      $,
      selector: site.selectors.detailManga.chapterLink,
      baseUrl: request.url,
      label: "CHAPTER_DETAIL",
      userData: { site },
      addRequests,
      sharedQueue,
      log
    });

    if (addedCount === 0) {
      log.warn(`⚠️ No valid chapter URLs could be extracted from ${chapterLinksCount} links`);
    }
  }
}
