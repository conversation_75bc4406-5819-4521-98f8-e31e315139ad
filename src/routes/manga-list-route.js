import { Request } from "crawlee";
import { extractNumberFromString } from "../utils/index.js";
import { extractAndEnqueueRequests } from "../utils/url-helpers.js";
import { getOrInitQueue } from "../shared.js";

export async function handleMangaList(context) {
  const { parseWithCheerio, log, request, enqueueLinks, addRequests } = context;
  const sharedQueue = await getOrInitQueue();
  const { site } = request.userData;

  if (!site) {
    log.error("🐞 No site information found in request userData.");
    return;
  }

  log.info(`🔎 Scraping ${request.url}`);

  let currentPage = 1;

  const $ = await parseWithCheerio();

  // Enqueue manga detail links from current page
  const mangaLinksCount = $(site.selectors.listManga.mangaLink).length;
  log.info(`📚 Found ${mangaLinksCount} manga links on current page`);

  if (mangaLinksCount > 0) {
    log.info(`🔗 Attempting to enqueue ${mangaLinksCount} MANGA_DETAIL requests...`);

    const addedCount = await extractAndEnqueueRequests({
      $,
      selector: site.selectors.listManga.mangaLink,
      baseUrl: request.url,
      label: "MANGA_DETAIL",
      userData: { site },
      addRequests,
      log
    });

    if (addedCount === 0) {
      log.warn(`⚠️ No valid manga URLs could be extracted from ${mangaLinksCount} links`);
    }
  } else {
    log.warn(`⚠️ No manga links found with selector: ${site.selectors.listManga.mangaLink}`);
  }

  const $nextPage = $(site.selectors.listManga.nextPage);
  const $lastPage = $(site.selectors.listManga.lastPage);

  if ($lastPage) {
    const lastPageNumber = extractNumberFromString($lastPage.attr("href"));
    log.info(`Last page number: ${lastPageNumber}`);

    const nextPageUrls = [];

    for (let i = currentPage + 1; i <= site.maxPages; i++) {
      if (i > lastPageNumber) {
        log.info(`Skipping page ${i} as it exceeds the last page number.`);
        continue;
      }
      const pageUrl = site.listPageUrl.replace("$page", String(i));
      const url = `${site.baseUrl}${pageUrl}`;

      nextPageUrls.push(
        new Request({
          url,
          userData: { site, label: "MANGA_LIST" },
          label: "MANGA_LIST",
        }),
      );
    }

    log.info(`📄 Adding ${nextPageUrls.length} additional pages to queue`);
    await sharedQueue.addRequests(nextPageUrls);
    log.info(`✅ Successfully added ${nextPageUrls.length} MANGA_LIST requests`);
    return;
  }
  if ($nextPage.length > 0 && currentPage <= site.maxPages) {
    currentPage++;
    await enqueueLinks({
      selector: site.selectors.listManga.nextPage,
      label: "MANGA_LIST",
      requestQueue: sharedQueue,
      transformRequestFunction: (request) => {
        request.userData.site = site;
        return request;
      },
    });
  }
}
